#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
聊天处理模块
包含各种聊天功能的处理器（LLM、RAG、DATAQA、CAR、ALL、SEARCH）
"""

import asyncio
import json
from typing import AsyncGenerator, <PERSON><PERSON>
from loguru import logger

from frontend.api.client import APIClient, ResponseProcessor
from frontend.utils.helpers import TimingManager, RequestHelper, HistoryFormatter, ValidationHelper, LogHelper


class BaseChatHandler:
    """基础聊天处理器"""

    def __init__(self, api_client: APIClient, chat_app):
        """初始化基础聊天处理器

        Args:
            api_client: API客户端
            chat_app: ChatApp实例
        """
        self.api_client = api_client
        self.chat_app = chat_app
        self.response_processor = ResponseProcessor()
        self.first_token_time = None
    
    def _validate_input(self, query: str) -> bool:
        """验证输入
        
        Args:
            query: 用户查询
            
        Returns:
            是否有效
        """
        return ValidationHelper.is_valid_query(query)
    
    def _create_timing_manager(self) -> TimingManager:
        """创建计时管理器

        Returns:
            计时管理器实例
        """
        return TimingManager()

    def _record_first_token_time(self, timing: TimingManager):
        """记录首token时间

        Args:
            timing: 计时管理器
        """
        if self.first_token_time is None:
            import time
            self.first_token_time = time.time() - timing.start_time


class LLMChatHandler(BaseChatHandler):
    """LLM聊天处理器"""
    
    async def chat_stream(self, query: str, model_id: str, user_id: str,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str], None]:
        """LLM问答 - 流式输出版本
        
        Args:
            query: 用户查询
            model_id: 模型ID
            user_id: 用户ID
            history_display: 历史显示
            
        Yields:
            (history_display, reasoning_content, content_content, first_token_time)
        """
        logger.info("LLM chat_stream被调用")
        
        if not self._validate_input(query):
            yield history_display, "", "", "0.0秒"
            return
        
        # 初始化计时管理器
        timing = self._create_timing_manager()
        timing.start_timing()
        self.first_token_time = None  # 重置首token时间

        # 准备请求参数
        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("llm"),
            stream=True
        )

        # 记录日志
        logger.info(LogHelper.format_log_message(
            "llm", "请求开始", payload["msg_id"], user_id, model_id, query
        ))

        # 调用API并实时更新
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("llm-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                # 记录首token时间
                if chunk_content and self.first_token_time is None:
                    self._record_first_token_time(timing)

                if chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    first_token_display = f"{self.first_token_time:.1f}秒" if self.first_token_time else "0.0秒"
                    yield history_display, reasoning_content, content_content, first_token_display
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    first_token_display = f"{self.first_token_time:.1f}秒" if self.first_token_time else "0.0秒"
                    yield history_display, reasoning_content, content_content, first_token_display
                    
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "llm", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            first_token_display = f"{self.first_token_time:.1f}秒" if self.first_token_time else "0.0秒"
            yield history_display, reasoning_content, content_content, first_token_display
        
        # 更新历史对话
        self.chat_app.add_to_history("llm", query, content_content)
        
        # 更新历史显示
        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time()
        )
        
        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        
        logger.info(LogHelper.format_log_message(
            "llm", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        first_token_display = f"{self.first_token_time:.1f}秒" if self.first_token_time else "0.0秒"
        yield final_history, reasoning_content, content_content, first_token_display


class RAGChatHandler(BaseChatHandler):
    """RAG聊天处理器"""
    
    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str], None]:
        """RAG问答 - 流式输出版本
        
        Args:
            query: 用户查询
            model_id: 模型ID
            user_id: 用户ID
            top_k: 检索数量
            history_display: 历史显示
            
        Yields:
            (history_display, formatted_reference, reasoning_content, content_content)
        """
        logger.info("RAG chat_stream被调用")
        
        if not self._validate_input(query):
            yield history_display, "", "", ""
            return
        
        # 初始化计时管理器
        timing = self._create_timing_manager()
        timing.start_timing()
        self.first_token_time = None  # 重置首token时间

        # 准备请求参数
        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("rag"),
            stream=True,
            top_k=top_k
        )

        # 记录日志
        logger.info(LogHelper.format_log_message(
            "rag", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        # 调用API并实时更新
        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("rag-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                # 记录首token时间
                if chunk_content and self.first_token_time is None:
                    self._record_first_token_time(timing)

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                    
        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "rag", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content
        
        # 更新历史对话
        self.chat_app.add_to_history("rag", query, content_content)
        
        # 更新历史显示
        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time()
        )
        
        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)
        
        logger.info(LogHelper.format_log_message(
            "rag", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content


class DataQAChatHandler(BaseChatHandler):
    """DATAQA聊天处理器"""

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str], None]:
        """DATAQA问答 - 流式输出版本"""
        logger.info("DATAQA chat_stream被调用")

        if not self._validate_input(query):
            yield history_display, "", "", ""
            return

        timing = self._create_timing_manager()
        timing.start_timing()
        self.first_token_time = None  # 重置首token时间

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("dataqa"),
            stream=True,
            top_k=top_k
        )

        logger.info(LogHelper.format_log_message(
            "dataqa", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("data-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                # 记录首token时间
                if chunk_content and self.first_token_time is None:
                    self._record_first_token_time(timing)

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "dataqa", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        self.chat_app.add_to_history("dataqa", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)

        logger.info(LogHelper.format_log_message(
            "dataqa", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content


class CarChatHandler(BaseChatHandler):
    """汽车知识库聊天处理器"""

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str], None]:
        """汽车知识库问答 - 流式输出版本"""
        logger.info("CAR chat_stream被调用")

        if not self._validate_input(query):
            yield history_display, "", "", ""
            return

        timing = self._create_timing_manager()
        timing.start_timing()
        self.first_token_time = None  # 重置首token时间

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("car"),
            stream=True,
            top_k=top_k,
            knowledge="car"
        )

        logger.info(LogHelper.format_log_message(
            "car", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("rag-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                # 记录首token时间
                if chunk_content and self.first_token_time is None:
                    self._record_first_token_time(timing)

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "car", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        self.chat_app.add_to_history("car", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)

        logger.info(LogHelper.format_log_message(
            "car", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content


class AllChatHandler(BaseChatHandler):
    """全库问答聊天处理器"""

    async def chat_stream(self, query: str, model_id: str, user_id: str, top_k: int,
                         history_display: str) -> AsyncGenerator[Tuple[str, str, str, str], None]:
        """全库问答 - 流式输出版本"""
        logger.info("ALL chat_stream被调用")

        if not self._validate_input(query):
            yield history_display, "", "", ""
            return

        timing = self._create_timing_manager()
        timing.start_timing()
        self.first_token_time = None  # 重置首token时间

        payload = RequestHelper.create_payload(
            query=query,
            user_id=user_id,
            model_id=model_id,
            history=self.chat_app.get_conversation_history("allqa"),
            stream=True,
            top_k=top_k
        )

        logger.info(LogHelper.format_log_message(
            "all", "请求开始", payload["msg_id"], user_id, model_id, query, top_k=top_k
        ))

        reference_content = ""
        reasoning_content = ""
        content_content = ""

        try:
            async for chunk in self.api_client.stream_api_call("all-qa", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                # 记录首token时间
                if chunk_content and self.first_token_time is None:
                    self._record_first_token_time(timing)

                if chunk_type == "reference":
                    timing.start_reference_timing()
                    reference_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "reasoning":
                    timing.start_reasoning_timing()
                    reasoning_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content
                elif chunk_type == "content":
                    timing.start_content_timing()
                    content_content += chunk_content
                    formatted_reference = self.response_processor.format_reference_display(reference_content)
                    yield history_display, formatted_reference, reasoning_content, content_content

        except Exception as e:
            error_msg = f"**API调用失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "all", "异常", payload["msg_id"], user_id, model_id, query, error=str(e)
            ))
            content_content = error_msg
            formatted_reference = self.response_processor.format_reference_display(reference_content)
            yield history_display, formatted_reference, reasoning_content, content_content

        self.chat_app.add_to_history("allqa", query, content_content)

        new_history_entry = HistoryFormatter.format_history_entry(
            query=query,
            content=content_content,
            total_time=timing.get_total_time(),
            reference_time=timing.get_reference_time(),
            reasoning_time=timing.get_reasoning_time(),
            content_time=timing.get_content_time()
        )

        final_history = HistoryFormatter.append_to_history(history_display, new_history_entry)
        formatted_reference = self.response_processor.format_reference_display(reference_content)

        logger.info(LogHelper.format_log_message(
            "all", "请求结束", payload["msg_id"], user_id, model_id, query, content=content_content[:100]
        ))

        yield final_history, formatted_reference, reasoning_content, content_content


class SearchHandler(BaseChatHandler):
    """搜索处理器"""

    async def search_stream(self, query: str, user_id: str, top_k: int) -> AsyncGenerator[str, None]:
        """检索功能 - 流式输出版本"""
        logger.info("search_stream被调用")

        if not self._validate_input(query):
            yield ""
            return

        timing = self._create_timing_manager()
        timing.start_timing()

        payload = {
            "query": query,
            "user_id": user_id,
            "msg_id": RequestHelper.generate_request_id(),
            "top_k": top_k
        }

        logger.info(LogHelper.format_log_message(
            "search", "请求开始", payload["msg_id"], user_id, "", query, top_k=top_k
        ))

        search_results = ""

        try:
            async for chunk in self.api_client.stream_api_call("search", payload):
                chunk_type = chunk.get("type", "")
                chunk_content = chunk.get("content", "")

                if chunk_type == "reference":
                    search_results += chunk_content
                    formatted_results = self.response_processor.format_reference_display(search_results)
                    yield formatted_results

        except Exception as e:
            error_msg = f"**检索失败:** {str(e)}"
            logger.error(LogHelper.format_log_message(
                "search", "异常", payload["msg_id"], user_id, "", query, error=str(e)
            ))
            search_results = error_msg
            yield search_results

        final_time = timing.get_total_time()
        logger.info(LogHelper.format_log_message(
            "search", "请求结束", payload["msg_id"], user_id, "", query,
            time=f"{final_time:.2f}秒"
        ))

        formatted_results = self.response_processor.format_reference_display(search_results)
        yield formatted_results
