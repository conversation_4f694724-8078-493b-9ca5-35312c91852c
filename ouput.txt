data: {"type": "reference", "content": "[{\"id\": \"f8a6abeb-1eba-4e1e-b845-e3b045ecf250\", \"content\": \"<strong>会议主题：2022 年 9 月 21 号 </strong><strong>PCB</strong><strong>评审群组会议纪要</strong>\\n3 M9B M9 <strong>---9 月 29 号 堆叠锁定</strong><strong> </strong><strong> </strong>\\n1. PMU 与电感与射频 PA 本体不要上下重叠\\n2. 射频 2G PA 距离原则 15MM\\n3. 射频 U0600PA 与对应滤波器就近放置，不要太远\\n4. 定位孔建议右下角与左上角\\n5. CABLE 座子 M9 与 M9B 需要保持一致，尽量考虑公共端最短\\n6. Camera 孔 主摄与微距开孔联通，M9 M9B 布局重新调整\\n7. 充电芯片是否可以胶水可以粘胶？\\n8. 主芯片屏蔽罩是否需要两件形式的？需要确认，目前单体屏蔽罩涂导热凝胶有覆盖不全风险\", \"user_id\": \"pcb_kb_user\", \"metadata_json\": {\"Header 1\": \"<strong>会议主题：2022 年 9 月 21 号 </strong><strong>PCB</strong><strong>评审群组会议纪要</strong>\", \"Header 2\": \"3 M9B M9 <strong>---9 月 29 号 堆叠锁定</strong><strong> </strong><strong> </strong>\", \"id\": \"20902\", \"doc_type\": \"sheet\", \"publish_time\": \"2025-04-28 19:03:34\", \"project_area\": \"011\", \"doc_url\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"doc_name\": \"FPC板厂工艺时间表\", \"tm\": \"2025-05-06 19:23:42\", \"importance\": 1.0}, \"score\": 0.032018442622950824}]", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "", "role": "assistant", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "\n", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "好的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "我现在", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "处理", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "用户", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的问题", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "：“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "项目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "什么时候", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "挂", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "？”", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "首先", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "根据", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "规则", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "用户", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "提到", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "挂", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "”", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "应该", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "对应", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "知识", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "库", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "中的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "”。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "接下来", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "我要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "在", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "项目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "编码", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "为", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "条", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "里", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "查找", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "里程碑", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "名称", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "为", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "“", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "”的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "时间节点", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "查看", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "里程碑", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "列表", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "发现", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "的", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "计划", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "开始", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "时间", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "是", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "3", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "确认", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "没有", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "其他", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "相关", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "节点", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "后", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "直接", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "返回", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "这个", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "日期", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "确保", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "格式", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "正确", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "只", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "显示", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "日期", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "部分", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "不需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "时间", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "整个", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "过程", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "快速", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "准确", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "，", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "避免", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "任何", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "错误", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "reasoning", "content": "。\n", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "\n\n", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "M", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "9", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "项目", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "需要", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "在", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "**", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "2", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "1", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "-", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "3", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "0", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "**", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "进行", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "挂", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "（", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "投", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "板", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "）。", "role": "", "finish_reason": "", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}

data: {"type": "content", "content": "", "role": "", "finish_reason": "stop", "msg_id": "YOUR_UUID", "conversation_id": "YOUR_UUID"}




参考frontend文件夹中的tmp.py，构建一个前端页面，要求如下：
1、 页面上要实现LLM问答API、RAG问答API、ATAQA问答API三个接口的调用。
2、前端页面只用支持流式输出，且思考过程和正式回复过程要在不同的文本框里。
3、RAG问答API、ATAQA问答API流式调用时，第一个返回的流式内容为知识库召回的参考内容，需要在页面左边进行显示。LLM问答API调用不会返回知识库召回内容，直接显示思考过程和回答内容。
4、有多轮对话功能，当用户输入新问题时，需要把该用户在当前页面的历史问答也返回给api接口。
5、页面上需要有清空历史会话功能，清空历史对话后，再发送问题时，传给api接口的历史对话为一个空列表。


使用gradio构建一个前端页面，要求如下：
1、实现API接口调用
- LLM问答API、RAG问答API、ATAQA问答API 三个接口分别实现。
- 支持流式输出
2、流式输出与分区显示
- 页面两个分区，最左侧为参数配置区域，右侧为对话区域。
- RAG/ATAQA 接口首次流内容为知识库参考，显示在对话区域的左半部分。思考过程和正式回复分别显示在不同文本框，均在页面右侧，其中思考过程在正式回复上方
- LLM问答接口，展示在对话区域，思考过程和正式回复分别显示在不同文本框，其中思考过程在正式回复上方
3、多轮对话
- 记录历史问答，每次提问时带上历史对话发给API。
- 历史对话区展示所有轮次。
4、清空历史会话
- 点击按钮，清空历史对话记录，下次提问时，传给API的历史对话为一个空列表。
5、api调用方式请参考test_api.py
LLM问答API (/api/v1/llm-qa)
RAG问答API (/api/v1/rag-qa)
DATAQA问答API (/api/v1/data-qa)
6、api接口返回的内容格式如下，其中LLM问答API，type字段没有reference。

| 字段名          | 可能值                                                                 | 描述                                                                                                                                 |
|----------------|-----------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| conversation_id | 字符串                                                                | Ω会话Id                                                                                                                              |
| msg_id         | 字符串                                                                | 消息Id                                                                                                                               |
| type           | ● reference<br>● content 表示模型的正式回答部分<br>● reasoning 表示模型的内部思考或推理过程 | 输出的内容类型                                                                                                                       |
| content        | 文本片段或空字符串                                                    | 模型输出的文本内容。结合 type 字段使用：<br>- 当 type 为 reasoning 时：思考推理内容<br>- 当 type 为 content 时：正式回答内容<br>- 当 type 为 reference 时：引用内容 |
| role           | ● assistant                                                           | 对话中发言者的角色，用于区分上下文，首次出现时非空                                                                                    |
| finish_reason  | ● ""：输出尚未结束，后续还有数据<br>● stop：输出正常停止（例如，模型完成回答）<br>● length：达到 max_tokens 限制 | 输出结束的原因。None 表示输出仍在继续；非 None 值表示输出已完成                                                                        |
7、api接口返回的内容格式如下：

data: {"type": "reference", "content": "[{\"title\": \"FPC板厂工艺时间表\", \"content\": \"FPC 点击微动失效原因汇总", \"docName\": \"FPC板厂工艺时间表\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"sheetName\": \"\"}, {\"title\": \"FPC板厂工艺时间表\", \"content\": \"原因分析\\nFPC 断线位置\\n**核心结论：断线位置集中转轴盖对应位置，****FPC**** 中间区域出现裂纹，然后往两边延伸，内层存在裂纹；\", \"docName\": \"FPC板厂工艺时间表\", \"docUrl\": \"https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g\", \"sheetName\": \"FPC 断线位置\"}]", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "", "role": "assistant", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "\n", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "reasoning", "content": "好的", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
...
data: {"type": "reasoning", "content": "单独", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
data: {"type": "content", "content": "对于", "role": "", "finish_reason": "", "msg_id": "UUID", "conversation_id": "UUID"}
...
data: {"type": "content", "content": "", "role": "", "finish_reason": "stop", "msg_id": "UUID", "conversation_id": "UUID"}



本地使用test_api.py测试，日志结果如下：
2025-07-09 11:58:10.696 | INFO | api.app:startup_event:43 | 应用程序启动
INFO:     Application startup complete.
INFO:     127.0.0.1:60618 - "GET /api/v1/health HTTP/1.1" 200 OK
2025-07-09 11:58:13.961 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:llm_qa:76 | LLM问答请求开始: 库存周转率如何计算？
2025-07-09 11:58:13.983 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:llm_qa:79 | 历史记录条数: 2
INFO:     127.0.0.1:60620 - "POST /api/v1/llm-qa HTTP/1.1" 200 OK
enable_thinking: False
2025-07-09 11:58:13.985 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | api.routes:generate_stream:83 | 开始生成流式响应
2025-07-09 11:58:13.986 | INFO | request_id=5381e6c8-6d01-49db-8a6f-7da23ab421fc | pipelines.llm_qa:_build_messages:36 | 构建消息列表：[{'role': 'system', 'content': '\n你是一个供应链领域知识专家，请根据用户的问题进行回复。\n'}, {'role': 'user', 'content': '什么是供应链管理？'}, {'role': 'assistant', 'content': '供应链管理是指对供应链中的信息流、物流和资金流进行计划、组织、协调与控制的过程。'}]
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, refusal=None, role='assistant', tool_calls=None), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='\n'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='好的'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-4b48955cecd34fb3b0bf0597814fb6c3', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='，'), finish_reason=None, index=0, logprobs=None)], created=1752033494, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
...
2025-07-09 11:59:26.316 | INFO | request_id=2ff49141-48fc-4bca-b7e0-1606e48c2671 | api.routes:generate_stream:98 | LLM流式问答完成，耗时: 20.83秒
前端页面测试，日志如下：
2025-07-09 11:57:03.119 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:llm_qa:76 | LLM问答请求开始: 你好
2025-07-09 11:57:03.175 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:llm_qa:79 | 历史记录条数: 0
INFO:     127.0.0.1:57807 - "POST /api/v1/llm-qa HTTP/1.1" 200 OK
2025-07-09 11:57:03.180 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | api.routes:generate_stream:83 | 开始生成流式响应
enable_thinking: False
2025-07-09 11:57:03.181 | INFO | request_id=57e4189e-14cf-48c8-a776-d45482bbb800 | pipelines.llm_qa:_build_messages:36 | 构建消息列表：[{'role': 'system', 'content': '\n你是一个供应链领域知识专家，请根据用户的问题进行回复。\n'}]
chunk: ChatCompletionChunk(id='chatcmpl-ad8f69e8c906423d979c42fc4489fed1', choices=[Choice(delta=ChoiceDelta(content='', function_call=None, refusal=None, role='assistant', tool_calls=None), finish_reason=None, index=0, logprobs=None)], created=1752033423, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)
chunk: ChatCompletionChunk(id='chatcmpl-ad8f69e8c906423d979c42fc4489fed1', choices=[Choice(delta=ChoiceDelta(content=None, function_call=None, refusal=None, role=None, tool_calls=None, reasoning_content='\n'), finish_reason=None, index=0, logprobs=None)], created=1752033423, model='Qwen3-32B', object='chat.completion.chunk', service_tier=None, system_fingerprint=None, usage=None)

前端服务的日志：
INFO:httpx:HTTP Request: POST http://localhost:8080/api/v1/llm-qa "HTTP/1.1 200 OK"
INFO:api_client:开始接收流式响应: llm-qa
前端收到chunk: type=content, content='', finish_reason=''
前端收到chunk: type=reasoning, content='
', finish_reason=''

请分析，为什么使用test_api.py测试LLM问答api时，api服务的日志都正常。而在前端页面进行测试时，LLM问答API的日志显示，输出两个以后就不再输出了。



接口调整：
1、llm问答接口开放temperature等参数调整
2、硬工知识库问答接口开放top_k等参数调整
3、R平台问答接口开放



前端修改建议：
1、参考知识召回、思考过程、回复内容、增加耗时记录
2、三个api接口对应不同的服务，对话不能串用
3、思考过程框、回复内容框、知识库参考都要固定大小
4、问题库AI移到右侧
5、左侧配置拦进一步缩小，左侧只有配置有关内容，右侧整体区域增大
6、发送按钮移动到输入问题框右侧，清空历史按钮移到发送按钮右侧


前端修改建议：
1、耗时应该分开统计，放在对应label的右边，如获得知识库参考的耗时、思考过程的耗时、回复内容的耗时
2、知识库参考、思考过程、回复内容、对话历史这几个框里的内容都需要是支持markdown格式渲染
3、整体页面放大，缩小配置区域，放大知识库参考、思考过程和回复内容框
4、发送按钮调整较小
5、左侧配置显示如下信息：
  -对话配置：用户id、对话id
 - 模型配置
 - 检索配置： 检索数量上限调整为20
 - 对话历史清空按钮

  
请按照如下要求调整前端页面：
1、页面应该自适应浏览器窗口大小，铺满整个浏览器，放大知识库参考、思考过程和回复内容框、发送按钮调整较小
2、知识库参考、思考过程、回复内容、对话历史这几个框里的内容都需要是支持markdown格式渲染，不同界面组件应该有明确的边界。
3、选择不同api接口时，页面布局应该初始化好。
4、知识库参考、思考过程，回复内容需要有label
5、耗时应该分开统计，放在对应label的右边，如获得知识库参考的耗时、思考过程的耗时、回复内容的耗时，耗时放在label的右边，紧挨着label
6、把问题库AI大标题移到最左侧
7、左侧共有如下信息：
 - 大标题
  -对话配置：用户id、对话id
 - 模型配置
 - 检索配置： 检索数量上限调整为20
 - 对话历史清空按钮
8、页面设计尽可能简洁美观

请按照如下建议继续修改前端页面：
1、思考过程、回复内容、知识库参考框都可以放到最大，有复制选项
2、当浏览器窗口较小时，页面不支持上下滑动，导致看不到底部的信息
3、当浏览器窗口较大时，依然没有做到自适应窗口大小，而是都是集中到页面左侧。
4、耗时没有正常显示，应该将获得知识库参考的耗时、思考过程的耗时、回复内容的耗时，耗时放在每个模块label的右边，紧挨着label，耗时单位为秒，保留一位小数。
5、清空历史对话按钮报错


如图所示，前端页面如下几个问题：
1、当浏览器窗口较大时，依然没有做到自适应窗口大小，而是都是集中到页面左侧。
2、知识库参考、思考过程和回复内容，几个label右侧的时间一直停留在0.0秒，没有正常显示耗时。知识库参考计时规则为从发送query，到获得知识库参考结束。思考过程计时规则为从获得知识库参考结束，到获得思考过程结束。回复内容计时规则为从获得思考过程结束，到获得回复内容结束。
3、知识库参考、思考过程和回复内容、 对话历史几个框没有放到最大选项，复制选项被框边缘遮挡。
4、知识库参考、思考过程和回复内容、 对话历史几个label应该紧贴下面的内容显示框，因为参考知识和模型回复内容较多，所以需要要紧凑布局，尽可能用多的地方展示这些信息。
5、知识库参考和数据参考里的具体内容，字体小一些。思考过程的字体也要小一些。
6、数据参考与知识库参考一样的设计。
7、在页面左侧增加首token响应时间，为从发送问题，到收到api第一个chunk的时间。


请按照如下建议修改前端界面：
1、以下几个label和对应的value显示上应该在同一行：用户id、对话id、首token响应时间、模型选择
2、知识库参考、思考过程、回复内容这几个标签后面的耗时依然未正常显示，一直显示时0.0。知识库参考计时规则为从发送query，到获得知识库参考结束。思考过程计时规则为从获得知识库参考结束，到获得思考过程结束。回复内容计时规则为从获得思考过程结束，到获得回复内容结束。
3、show_copy_button复制按钮被边缘圆角边框给遮挡了。
4、思考过程、知识库参考、对话历史、数据参考这几个textbox要有最大高度限制，不能随着内容的增多而一只调大高度。回复内容textbox需要根据输出内容的长度，动态调整高度。



请按照如下要求修改前端代码：
1、新增汽车知识库问答模块，对应的 api接口为 rag-qa 中的knowledge_type == "car"时的分支。
2、新增 all问答模块，对应的 api接口为 all-qa。
3、all问答模块和汽车知识库问答模块，请参考硬工知识库问答模块的实现方式。
4、新增一个检索模块，对应的 api接口为 search，对应的页面只需要显示输入问题和检索结果即可。


按照如下要求修改前端代码：
1、前端代码都在一个文件中，可维护性和可扩展性较差，请拆分多个文件，每个文件对应一个功能模块。增强代码的可扩展性。
2、不要调整任何页面的样式，和功能性的东西，只对代码进行拆解，拆解以后代码复用性应该会更好。
3、对应的修改启动脚本 run_gradio.py


请按照如下要求修改前端代码，
1、问题库AI 标题应该移到左侧配置栏。
2、调整硬工知识库问答、R 平台问答、汽车知识库、全库问答的布局。知识库参考应该在问题栏下面左侧的位置，思考过程和回复应该在知识库参考的右侧位置。
3、思考过程、知识库参考、对话历史、数据参考这几个textbox要有最大高度限制，不能随着内容的增多而一只调大高度。回复内容textbox需要根据输出内容的长度，动态调整高度。
4、思考过程框支持折叠。


按照如下要求继续修改代码：
1、页面布局调整不正确，知识库参考在问题栏下面左侧，思考过程和回复在知识库参考的下面。请把思考过程和回复内容调整到知识库参考的右侧。
2、去除知识库参考，思考过程、知识库参考、数据参考label旁边的记时显示。
3、页面采用紧凑布局，字体都调小。
4、首 token响应没有生效，首 token时间为从发送问题，到收到api第一个chunk的时间。




请按照如下建议

2025-07-11 10:39:44.356 | INFO | request_id=9add8b05-ea1b-4a90-998a-858b38678a0d | services.rerank_service:rerank:40 | Rerank 结果: {'scores': [0.20307461854333342, 0.2018132246607271, 0.9860442868719435, 0.94815453060333, 0.791821063573637, 0.5907792048683922, 0.9477691473244688, 0.9820137881297458, 0.9818752789240509, 0.9788748101069611, 0.10818895887524603, 0.9445872087380701, 0.519521321952093, 0.9433475772802015, 0.014281935472223657, 0.014392338469985163, 0.9469902270491015, 0.8128673246376009, 0.9742394433111607, 0.13296423160844617]}

检索到的知识：


{'id': '1d3c4c16-cd33-454f-ad0e-797aab5c412a', 'content': '| 序号 | 模块 | 检查内容 | 图示 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 4.28 | 点胶 | 点胶芯片，如SOC和POP-DDR等填充面积＞80%芯片面积；胶水原则上不允许溢胶到芯片表面，如果溢胶到表面 不允许Z向粘连屏蔽框等；点胶不允许溢胶到禁止点胶器件，如WLCSP，绕线电感，薄膜电阻，部分板震敏感电容等；点胶边4mm非点胶边1.5mm点胶不允许溢胶到屏蔽框和结构件等造成点胶器件与结构件等粘连带来应力风险； |  | Placement | H |  |  |  |  |  |  |  |', 'user_id': 'pcb_kb_user', 'metadata_json': {'id': '20337', 'Header 1': 'MIPCB Design Checklist总表', 'Header 2': '4-可靠性', 'doc_name': 'MIPCB Design Checklist总表', 'doc_type': 'sheet', 'publish_time': '2025-03-26 20:37:24', 'project_area': '011', 'doc_url': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'tm': '2025-05-06 19:23:48', 'importance': 100}, 'score': 0.027146130794993832}


{'id': 'eb97659e-5004-43c4-9da7-772e70da37e0', 'content': '标准内容 Standard content\n<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>\n6.3.5 尾部点胶要求\n#### *******. 16 pin 点胶规格（Root & Tree & Geek & Terse & Ark）  \n如下图红色区域 Type C 焊脚需点胶覆盖.  \nL9.40*L1.70*L1.40 底部填充 >90%，点胶型号：H2004/3940tx/62208。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4E3Ru8wjRxqQGkDjFghGJpe)  \n#### *******. 24 pin 点胶规格（Peak & Bolt）  \n规格一（Peak）  \n如下图 Type C 焊脚需点底层胶。  \nL7.5*H1.3*T0.8，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4XaEtAv5PK1cnM57Z3xdEne)  \n规格二（Bolt）  \n如图区域 Type C 焊脚需点底层胶。  \nL11.00*W2.80*T0.30，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4zZ6dzxoWK7DzMyoG8nkSYg)', 'user_id': 'pcb_kb_user', 'metadata_json': {'Header 1': '标准内容 Standard content', 'Header 2': '<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>', 'Header 3': '6.3.5 尾部点胶要求', '\ufeffid': '20902', 'doc_type': 'sheet', 'publish_time': '2025-04-28 19:03:34', 'project_area': '011', 'doc_url': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'doc_name': 'FPC板厂工艺时间表', 'tm': '2025-05-06 19:23:42', 'importance': 1.0}, 'score': 0.025934278350515462}


{'id': '2e397194-287e-4480-96d1-7b9e567eda27', 'content': 'PCB 开发部需求跟踪\n<tr>\n<td>44<br/></td><td>点击微动测试失效；<br/></td><td>压延铜和电解铜弯折寿命、收益及风险评估<br/></td><td>北京开发二部<br/></td><td>12月30日<br/></td><td>2023/3/31<br/></td><td>@李克忠 <br/></td><td>专项验证<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>改成压延铜，M1验证目前效果不大<br/></td></tr>\n<tr>\n<td>45<br/></td><td>点击微动位置选取相对随意且测试规范不统一<br/></td><td>点击微动位置选取相对随意（过严格/过宽松），测试时对于是否同时点击无统一规范。<br/></td><td>北京开发一部<br/></td><td>12月30日<br/></td><td>2023/1/13<br/></td><td>@李克忠 <br/></td><td>20230105：拉通完成<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>点击微动位置升级，本月中旬有初稿<br/></td></tr>\n<tr>\n<td>46<br/></td><td>M18 MIC FPC先组装到电池盖上，在上传BOM时需要确定骨架位置。<br/></td><td>常规项目FPC BOM上传到结构通用料骨架下，但会有一些特殊情况，如M18 MIC FPC需要上传到电池盖预组骨架；上传BOM前需要与结构/整机工艺确认哪条FPC应该上传到那个骨架？<br/></td><td>北京开发一部<br/></td><td>1月6日<br/></td><td><nil><br/></td><td>@宣立杰 <br/></td><td>跟组装顺序有关，每个项目都不一样，上传BOM前确认位置<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>正在实现自动化<br/></td></tr>\n<tr>\n<td>47<br/></td><td>M3 闪光灯FPC维信点胶是全包裹，福莱赢是半包裹，点胶厚度不一致。福莱赢电容与Deco短路<br/></td><td>FPC上阻容感点胶策略，点胶规范<br/></td><td>北京开发二部<br/></td><td>2月2日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>chip：全包裹，X,Y向常规0.6，极限0.3mm；Z向最大0.3mm<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>辅料  点胶 预折弯后续建立统一规范文件。材料库增加点胶胶水型号。<br/></td></tr>\n<tr>\n<td>48<br/></td><td>M18 OLED FPC样品，出现明显色差，供应商需求增加酸洗制程，是否会对铜面造成微蚀，从而影响弯折性能。<br/></td><td>问题描述：维信oled FPC减短验证样品铜面异色，拉力和电测均pass，已安排加做热应力测试发生原因：铜箔原材料领取直接叠板压胶，电压烘烤后bonding面有一层抗氧化膜经过高温烘烤后固化，前处理不容易清洗掉外层蚀刻后板面色差;改善措施：多层板bonding铜箔使用酸洗流程去除抗氧化膜<br/></td><td>北京开发一部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>制程分析酸洗会有0.3~0.5um的咬蚀量，建议N代预研阶段开始弯折验证ok后再导入；M18现阶段不建议增加酸洗制程；鹏鼎下料后无酸洗制程，未反馈铜面异色的问题。<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>目前关闭<br/></td></tr>\n<tr>\n<td>49<br/></td><td>FPC 各厂商预折弯效果差异明显，导致点击微动测试结果有差异。<br/></td><td>FPC预折弯各厂商差异明显，需要统一验收标准，减少差异。<br/></td><td>上海开发部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>规范供应商试产与量产阶段弯折治具设计与弯折参数；<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>统一整理，与上面重复<br/></td></tr>\n<tr>\n<td>50<br/></td><td>N18转轴预研尽量早点把方案同步给PDU<br/></td><td>N18转轴预研开模后再同步给项目设计FPC，发板时间紧张，方案考虑不充分。需求尽量早一些同步方案给PDU。<br/></td><td>北京开发一部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@张建刚 <br/></td><td>N18目前两个主要技术点：五明治和Cavity，预研方案验证pass，五明治ADCP总结中，POF优化验证中<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>目前整理预研报告<br/></td></tr>\n<tr>\n<td>51<br/></td><td>M16,M20,M9 点击微动测试点在连接器边缘，导致连接器锡裂。<br/></td><td>测试点位选择需要避开连接器，加进测试规范。<br/></td><td>上海开发部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@苑冉旭 <br/></td><td>新增规范,点击头中心放在弯折区,偏离弯折区边缘<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>点击微动<br/></td></tr>\n<tr>\n<td>52<br/></td><td>整理笔电设计规范<br/></td><td>B21首项目布局和走线问题较多<br/></td><td>北京开发二部<br/></td><td>20230303<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td>3月3号后，开始梳理，先搜集文档与规范<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>53<br/></td><td>基带摄像头相关规范影响堆叠<br/></td><td>摄像头本体同面与电感距离规范需要拉通统一策略，当前基带中台规范为2.5mm<br/></td><td>深圳开发部<br/></td><td>2023/02/27<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td><nil><br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>54<br/></td><td>N代产品量产转昌平工厂，相关DFA要求较为严格<br/></td><td>在N代产品堆叠FIX之前与智能制造拉通昌平工厂相关DFA要求<br/></td><td>深圳开发部<br/></td><td>2023/03/10<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>55<br/></td><td>对于FPC上的NTC电阻是否点胶无统一标准<br/></td><td>M1 FLASS FPC未了解决散热问题，将原0402 NTC电阻移动至闪光灯附近，由于空间限制，更换为01005，担心撞件，需求点胶，但热设计不希望点胶，从而获得更准确的温度0630：新增器件距离闪光灯距离要求确认？<br/></td><td>北京开发一部<br/></td><td>2023/3/31<br/></td><td><nil><br/></td><td>@李克忠 <br/></td><td>基于M18项目验证结论，点胶对于温度灵敏度有影响，NTC不点胶<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>', 'user_id': 'pcb_kb_user', 'metadata_json': {'Header 1': 'PCB 开发部需求跟踪', '\ufeffid': '20902', 'doc_type': 'sheet', 'publish_time': '2025-04-28 19:03:34', 'project_area': '011', 'doc_url': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'doc_name': 'FPC板厂工艺时间表', 'tm': '2025-05-06 19:23:42', 'importance': 1.0}, 'score': 0.025774005972025775}



重排后的知识: 

{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.29999999999999 | 结构加强胶器件侧边点胶，点胶路径按宽度1.0mm设计，推荐1.2mm (路径内不允许布点胶敏感性器件或测试点等），点胶高度不超过本体。 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)'}


{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.28999999999999 | UV胶或结构加强胶预留器件单边0.6mm作为点胶溢胶区，溢胶区内不允许布点胶敏感性器件或工厂测试点等；点胶避让高度为器件高度+0.5mm。 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)'}


{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 4.28 | 点胶 | 点胶芯片，如SOC和POP-DDR等填充面积＞80%芯片面积；胶水原则上不允许溢胶到芯片表面，如果溢胶到表面 不允许Z向粘连屏蔽框等；点胶不允许溢胶到禁止点胶器件，如WLCSP，绕线电感，薄膜电阻，部分板震敏感电容等；点胶边4mm非点胶边1.5mm点胶不允许溢胶到屏蔽框和结构件等造成点胶器件与结构件等粘连带来应力风险； |  | Placement | H |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '4-可靠性'}


{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.21999999999998 | 点胶线路，只允许"I,L,Z”三种，防止点胶后气泡产生，点胶气泡量小于元件面积的10%。 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)'}


{'title': 'FPC板厂工艺时间表', 'content': '标准内容 Standard content\n<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>\n6.3.5 尾部点胶要求\n#### *******. 16 pin 点胶规格（Root & Tree & Geek & Terse & Ark）  \n如下图红色区域 Type C 焊脚需点胶覆盖.  \nL9.40*L1.70*L1.40 底部填充 >90%，点胶型号：H2004/3940tx/62208。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4E3Ru8wjRxqQGkDjFghGJpe)  \n#### *******. 24 pin 点胶规格（Peak & Bolt）  \n规格一（Peak）  \n如下图 Type C 焊脚需点底层胶。  \nL7.5*H1.3*T0.8，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4XaEtAv5PK1cnM57Z3xdEne)  \n规格二（Bolt）  \n如图区域 Type C 焊脚需点底层胶。  \nL11.00*W2.80*T0.30，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4zZ6dzxoWK7DzMyoG8nkSYg)', 'docName': 'FPC板厂工艺时间表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'sheetName': '<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>'}


{'title': 'FPC板厂工艺时间表', 'content': '标准内容 Standard content\n<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>\n6.3.5 尾部点胶要求\n#### *******. 16 pin 点胶规格（Root & Tree & Geek & Terse & Ark）  \n如下图红色区域 Type C 焊脚需点胶覆盖.  \nL9.40*L1.70*L1.40 底部填充 >90%，点胶型号：H2004/3940tx/62208。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4E3Ru8wjRxqQGkDjFghGJpe)  \n#### *******. 24 pin 点胶规格（Peak & Bolt）  \n规格一（Peak）  \n如下图 Type C 焊脚需点底层胶。  \nL7.5*H1.3*T0.8，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4XaEtAv5PK1cnM57Z3xdEne)  \n规格二（Bolt）  \n如图区域 Type C 焊脚需点底层胶。  \nL11.00*W2.80*T0.30，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4zZ6dzxoWK7DzMyoG8nkSYg)', 'docName': 'FPC板厂工艺时间表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'sheetName': '<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>'}


{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 17.1 | 点胶 | chip器件如点uv胶；XY溢胶范围小于等于0.6MM；点胶高度+0.3MM管控 |  | Layout | H |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '18-FPC_QC发板'}


{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 14.32 | 点胶 | chip器件如点uv胶；XY溢胶范围小于等于0.6mm；点胶高度+0.3mm管控 |  | Placement | H |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '14-FPC'}


{'title': 'FPC板厂工艺时间表', 'content': 'PCB 开发部需求跟踪\n<tr>\n<td>44<br/></td><td>点击微动测试失效；<br/></td><td>压延铜和电解铜弯折寿命、收益及风险评估<br/></td><td>北京开发二部<br/></td><td>12月30日<br/></td><td>2023/3/31<br/></td><td>@李克忠 <br/></td><td>专项验证<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>改成压延铜，M1验证目前效果不大<br/></td></tr>\n<tr>\n<td>45<br/></td><td>点击微动位置选取相对随意且测试规范不统一<br/></td><td>点击微动位置选取相对随意（过严格/过宽松），测试时对于是否同时点击无统一规范。<br/></td><td>北京开发一部<br/></td><td>12月30日<br/></td><td>2023/1/13<br/></td><td>@李克忠 <br/></td><td>20230105：拉通完成<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>点击微动位置升级，本月中旬有初稿<br/></td></tr>\n<tr>\n<td>46<br/></td><td>M18 MIC FPC先组装到电池盖上，在上传BOM时需要确定骨架位置。<br/></td><td>常规项目FPC BOM上传到结构通用料骨架下，但会有一些特殊情况，如M18 MIC FPC需要上传到电池盖预组骨架；上传BOM前需要与结构/整机工艺确认哪条FPC应该上传到那个骨架？<br/></td><td>北京开发一部<br/></td><td>1月6日<br/></td><td><nil><br/></td><td>@宣立杰 <br/></td><td>跟组装顺序有关，每个项目都不一样，上传BOM前确认位置<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>正在实现自动化<br/></td></tr>\n<tr>\n<td>47<br/></td><td>M3 闪光灯FPC维信点胶是全包裹，福莱赢是半包裹，点胶厚度不一致。福莱赢电容与Deco短路<br/></td><td>FPC上阻容感点胶策略，点胶规范<br/></td><td>北京开发二部<br/></td><td>2月2日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>chip：全包裹，X,Y向常规0.6，极限0.3mm；Z向最大0.3mm<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>辅料  点胶 预折弯后续建立统一规范文件。材料库增加点胶胶水型号。<br/></td></tr>\n<tr>\n<td>48<br/></td><td>M18 OLED FPC样品，出现明显色差，供应商需求增加酸洗制程，是否会对铜面造成微蚀，从而影响弯折性能。<br/></td><td>问题描述：维信oled FPC减短验证样品铜面异色，拉力和电测均pass，已安排加做热应力测试发生原因：铜箔原材料领取直接叠板压胶，电压烘烤后bonding面有一层抗氧化膜经过高温烘烤后固化，前处理不容易清洗掉外层蚀刻后板面色差;改善措施：多层板bonding铜箔使用酸洗流程去除抗氧化膜<br/></td><td>北京开发一部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>制程分析酸洗会有0.3~0.5um的咬蚀量，建议N代预研阶段开始弯折验证ok后再导入；M18现阶段不建议增加酸洗制程；鹏鼎下料后无酸洗制程，未反馈铜面异色的问题。<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>目前关闭<br/></td></tr>\n<tr>\n<td>49<br/></td><td>FPC 各厂商预折弯效果差异明显，导致点击微动测试结果有差异。<br/></td><td>FPC预折弯各厂商差异明显，需要统一验收标准，减少差异。<br/></td><td>上海开发部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>规范供应商试产与量产阶段弯折治具设计与弯折参数；<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>统一整理，与上面重复<br/></td></tr>\n<tr>\n<td>50<br/></td><td>N18转轴预研尽量早点把方案同步给PDU<br/></td><td>N18转轴预研开模后再同步给项目设计FPC，发板时间紧张，方案考虑不充分。需求尽量早一些同步方案给PDU。<br/></td><td>北京开发一部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@张建刚 <br/></td><td>N18目前两个主要技术点：五明治和Cavity，预研方案验证pass，五明治ADCP总结中，POF优化验证中<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>目前整理预研报告<br/></td></tr>\n<tr>\n<td>51<br/></td><td>M16,M20,M9 点击微动测试点在连接器边缘，导致连接器锡裂。<br/></td><td>测试点位选择需要避开连接器，加进测试规范。<br/></td><td>上海开发部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@苑冉旭 <br/></td><td>新增规范,点击头中心放在弯折区,偏离弯折区边缘<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>点击微动<br/></td></tr>\n<tr>\n<td>52<br/></td><td>整理笔电设计规范<br/></td><td>B21首项目布局和走线问题较多<br/></td><td>北京开发二部<br/></td><td>20230303<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td>3月3号后，开始梳理，先搜集文档与规范<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>53<br/></td><td>基带摄像头相关规范影响堆叠<br/></td><td>摄像头本体同面与电感距离规范需要拉通统一策略，当前基带中台规范为2.5mm<br/></td><td>深圳开发部<br/></td><td>2023/02/27<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td><nil><br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>54<br/></td><td>N代产品量产转昌平工厂，相关DFA要求较为严格<br/></td><td>在N代产品堆叠FIX之前与智能制造拉通昌平工厂相关DFA要求<br/></td><td>深圳开发部<br/></td><td>2023/03/10<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>55<br/></td><td>对于FPC上的NTC电阻是否点胶无统一标准<br/></td><td>M1 FLASS FPC未了解决散热问题，将原0402 NTC电阻移动至闪光灯附近，由于空间限制，更换为01005，担心撞件，需求点胶，但热设计不希望点胶，从而获得更准确的温度0630：新增器件距离闪光灯距离要求确认？<br/></td><td>北京开发一部<br/></td><td>2023/3/31<br/></td><td><nil><br/></td><td>@李克忠 <br/></td><td>基于M18项目验证结论，点胶对于温度灵敏度有影响，NTC不点胶<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>', 'docName': 'FPC板厂工艺时间表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'sheetName': ''}


{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.24999999999998 | 芯片点胶目前点胶边4mm内器件都有可能溢胶，非点胶边1.5mm器件可能溢胶，玻璃体及板震电容需移开：板震电容定义：0603及以上4.7uf/10uf/22uf/47uf陶瓷电容，VSYS、VPH_PWR、VBAT、VBUS(充电)、VRECT(充电)、VMID(充电)、CFLY(充电)、VBST(Smart PA)移不开的需平行于点胶路径摆放并与基带/工艺达成一致 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)'}


{'title': 'FPC板厂工艺时间表', 'content': 'PCB 开发部需求跟踪\n<tr>\n<td>33<br/></td><td><nil><br/></td><td>coverlay定义焊盘，考虑到溢胶，需要距离/压cu pad 0.25mm以上？<br/></td><td>北京开发一部<br/></td><td>11月18日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>1. 冲型工艺+pin孔贴合，距焊盘0.25mm2. 冲型工艺+机器自动贴合，距焊盘0.2mm3. 雷切工艺+机器自动贴合，距焊盘可以小于0.2mm以下，待验证<br/></td><td>是<br/></td><td><nil><br/></td><td>那现在器件FPC的库，建的都是0.13/0.15的，需要更新所有的封装和建库规则。@李克忠 @李艳禄 <br/></td><td>FPC封装建库单独讨论<br/></td></tr>\n<tr>\n<td>34<br/></td><td>M18侧键3D钢片，侧键PSA尺寸，点胶等，设计无法满足供应商制程要求，须尽快梳理辅料设计规范并与结构领域对齐，约束设计。<br/></td><td>拉通结构领域FPC辅料设计规范。<br/></td><td>北京开发一部<br/></td><td>11月18日<br/></td><td><nil><br/></td><td>@李克忠<br/></td><td>拉通中，预计12月底完成<br/></td><td>否<br/></td><td>FPC结构公差 V3 - 飞书云文档 (mioffice.cn)FPC/FPCA辅料背胶选材原则A.1 - 飞书云文档 (mioffice.cn)<br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>35<br/></td><td>M16T发生华通和超毅射频损耗差异0.3dB，分析铜箔华通选用RTF铜箔，健鼎选用HTE铜箔<br/></td><td>各个板厂铜箔选用原则分析汇总<br/></td><td>北京开发三部<br/></td><td>11月18日<br/></td><td><nil><br/></td><td>@王鑫 <br/></td><td>1. 华通和健鼎差异0.4dB，项目组接受差异；<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>预研验证<br/></td></tr>\n<tr>\n<td>36<br/></td><td>竞品分析友商晶体与热源距离相比我司要求宽松太多，按照当前要求，晶体无理想位置，对堆叠困扰很大<br/></td><td>晶体距离热源要求与友商差距巨大，相关的规范需要尽快拉通<br/></td><td>深圳开发部<br/></td><td>11月25日<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td>已经完成项目晶振统计，待与技术部拉通规则<br/></td><td>是<br/></td><td>晶振设计要求参考值与统计.xlsx 竞品晶体单板布局分析.pptx <br/></td><td><nil><br/></td><td>初步达成一致。<br/></td></tr>\n<tr>\n<td>37<br/></td><td>N2/N3/N16 极限滚筒主板断线<br/></td><td>应力断线精细化研究：普通次表层能扛应力、加粗 0.1/0.15能扛应力、L3层0.04mm走线扛应力情况 <br/></td><td>北京开发二部<br/></td><td>7月28日<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>39<br/></td><td>N2新宇P2模具分析油墨裂开<br/></td><td>FPC增加连接位 Solder Mask内缩0.1mm<br/></td><td>北京开发二部<br/></td><td>7月28日<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>40<br/></td><td>维修原理图<br/></td><td>售后和产线原理图H策略<br/></td><td>深圳开发部<br/></td><td>7月28日<br/></td><td>8月4日<br/></td><td>@李克忠<br/></td><td>工厂和高维都是通过mes系统查看原理图，原理图一些重要信息会被删除<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>41<br/></td><td><nil><br/></td><td>FPC： NPTH孔到器件中心公差 ：孔到器件中心公差结构按照0.1管控，质量管控CPK 1.33无法达成。保公差 or CPK？<br/></td><td>北京开发二部<br/></td><td>8月4日<br/></td><td>8月30日<br/></td><td>@李艳禄<br/></td><td>规范完善中<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>42<br/></td><td><nil><br/></td><td>FPC：180度弯折能够贴住不开胶的长度<br/></td><td>北京开发二部<br/></td><td>8月4日<br/></td><td>8月30日<br/></td><td>@李艳禄<br/></td><td>规范完善中<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>43<br/></td><td><nil><br/></td><td>完善FPC的两种点胶规范，点胶板边间距；<br/></td><td>北京开发三部<br/></td><td>8月4日<br/></td><td>8月30日<br/></td><td>@李艳禄<br/></td><td>规范完善中<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>44<br/></td><td><nil><br/></td><td>评估是否能在M16、N16、N16U项目顺切升级；<br/></td><td>北京开发三部<br/></td><td>8月4日<br/></td><td>8月30日<br/></td><td>@李艳禄<br/></td><td>规范完善中<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>45<br/></td><td><nil><br/></td><td>方正Cavity电测探针压伤焊盘<br/></td><td>北京开发二部<br/></td><td>8月4日<br/></td><td>8月18日<br/></td><td>@李克忠<br/></td><td>方正有跟李丹和质量做检讨<br/></td><td>是<br/></td><td>[map[id:2 type:attachment]]<br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>46<br/></td><td>N16U微断<br/></td><td>板厂通断测试阻值20Ω（含测试治具），需拆解识别严谨的测试阻值；<br/></td><td>北京开发三部<br/></td><td>8月4日<br/></td><td>8月30日<br/></td><td>@李艳禄<br/></td><td>规范完善中<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>47<br/></td><td>N1 闪光灯短路<br/></td><td>FPC电测治具那个阶段开<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>', 'docName': 'FPC板厂工艺时间表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'sheetName': ''}, {'title': 'FPC板厂工艺时间表', 'content': 'O81A DDR 点胶工艺可行性评估\n**结构：低风险；DFR：低风险；仿真：中风险；**  \n**工艺风险共识：**  \n1. **DDR 点胶未有项目量产及试产批量验证经验，只在 O2 摸底过点胶技术方案且未进行可靠性评估，建议优先考虑两件式屏蔽罩方案**\n2. **DDR 原则上不允许加胶，维修拆胶会造成 CPU 物料连带报废，维修风险高**  \n**点胶路径：**  \n单边点胶，DDR 两边均可作为点胶边  \n![](boxk4CIojWxA3Emi7pQpohpJQdg)  \n**屏蔽罩开窗要求：**  \n开孔边到元件边需 ≥1.2mm，目前上下两边不满足，需扩大开窗  \n![](boxk4dzjvJHw17XKXRv8FDnLdbZ)  \n**点胶敏感器件避让：**  \n需确认哪些器件不允许溢胶粘胶，以下为规范要求  \n![](boxk4eyVW4pwFAWztgFBMJ2O4Ff)  \n![](boxk4KwiGqD0yb1PGagR0wSa81g)', 'docName': 'FPC板厂工艺时间表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4sDk1eD6kdBnPcqzfFq7q1g', 'sheetName': ''}




reranked_retrieved_docs: [{'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: fanlei6, 角色代码: Radio_Expert, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5720270872116089}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: yuanyin, 角色代码: Planning_Window, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5693910121917725}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: xianglei, 角色代码: Antenna_Expert, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5689641833305359}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: xiemiaozhen, 角色代码: RFEngineer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5693809986114502}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: libin31, 角色代码: MCADDocReviewer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5690732002258301}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: yangqi1, 角色代码: MCADDocReviewer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5698750019073486}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: liuminghui, 角色代码: Radio_Expert, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5704677104949951}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: wanglichao, 角色代码: MCADDocReviewer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5693872570991516}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: zhanghang8, 角色代码: Radio_Expert, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5685823559761047}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: hongbaolin, 角色代码: MCADDocReviewer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5725761651992798}, 

{'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: wangcheng18, 角色代码: AccessoryME, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5710885524749756}, 


{'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: yulicheng, 角色代码: MCADDocReviewer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5688556432723999}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: lifeifei3, 角色代码: MCADDocReviewer, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5698296427726746}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: panxiduo, 角色代码: Radio_Expert, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5732514262199402}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: lifei18, 角色代码: Accessories_Purchase_Window, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5759498476982117}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: wangpengfei1, 角色代码: After_Sale_Team_Leader, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.56924968957901}, {'content': '项目编码: P8\n团队成员\n成员类型: USER, 成员编码: gongjiaxing1, 角色代码: Radio_Expert, 删除标识: 0', 'user_id': 'zty', 'metadata_json': {'doc_id': 'project_P8', 'chunk_idx': 3, 'n_chunk': 4, 'title': '项目编码: P8', 'url': '', 'docType': '', 'source': 'ipd'}, 'score': 0.5695374011993408}]



重排后的知识: 

[
{'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.29999999999999 | 结构加强胶器件侧边点胶，点胶路径按宽度1.0mm设计，推荐1.2mm (路径内不允许布点胶敏感性器件或测试点等），点胶高度不超过本体。 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.28999999999999 | UV胶或结构加强胶预留器件单边0.6mm作为点胶溢胶区，溢胶区内不允许布点胶敏感性器件或工厂测试点等；点胶避让高度为器件高度+0.5mm。 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}
]



, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 4.28 | 点胶 | 点胶芯片，如SOC和POP-DDR等填充面积＞80%芯片面积；胶水原则上不允许溢胶到芯片表面，如果溢胶到表面 不允许Z向粘连屏蔽框等；点胶不允许溢胶到禁止点胶器件，如WLCSP，绕线电感，薄膜电阻，部分板震敏感电容等；点胶边4mm非点胶边1.5mm点胶不允许溢胶到屏蔽框和结构件等造成点胶器件与结构件等粘连带来应力风险； |  | Placement | H |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '4-可靠性', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.21999999999998 | 点胶线路，只允许"I,L,Z”三种，防止点胶后气泡产生，点胶气泡量小于元件面积的10%。 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.23999999999998 | 芯片点胶需要可视窗，最小1.4mm*1.4mm 或 直径1.4mm的圆 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 17.1 | 点胶 | chip器件如点uv胶；XY溢胶范围小于等于0.6MM；点胶高度+0.3MM管控 |  | Layout | H |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '18-FPC_QC发板', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 14.32 | 点胶 | chip器件如点uv胶；XY溢胶范围小于等于0.6mm；点胶高度+0.3mm管控 |  | Placement | H |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '14-FPC', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': '手机Type C 连接器周边环境设计指导意见', 'content': '标准内容 Standard content\n<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>\n6.3.5 尾部点胶要求\n#### *******. 16 pin 点胶规格（Root & Tree & Geek & Terse & Ark）  \n如下图红色区域 Type C 焊脚需点胶覆盖.  \nL9.40*L1.70*L1.40 底部填充 >90%，点胶型号：H2004/3940tx/62208。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4E3Ru8wjRxqQGkDjFghGJpe)  \n#### *******. 24 pin 点胶规格（Peak & Bolt）  \n规格一（Peak）  \n如下图 Type C 焊脚需点底层胶。  \nL7.5*H1.3*T0.8，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4XaEtAv5PK1cnM57Z3xdEne)  \n规格二（Bolt）  \n如图区域 Type C 焊脚需点底层胶。  \nL11.00*W2.80*T0.30，底部填充 >80%，点胶型号：H2007+3940tx。  \nType C 尾部 0.8mm 空间之外需标 0.2mm 宽金线.  \n![](boxk4zZ6dzxoWK7DzMyoG8nkSYg)', 'docName': '手机Type C 连接器周边环境设计指导意见', 'docUrl': 'https://xiaomi.f.mioffice.cn/docs/dock4AqpqUWPMzpSfsByrNwZsRg', 'sheetName': '<strong>6.3 </strong><strong>SMT</strong><strong>要求</strong>', 'owner': '', 'update_time': '2025-03-26 19:56:47', 'doc_type': 'doc'}, {'title': 'PCB开发部需求跟踪', 'content': '<tr>\n<td>44<br/></td><td>点击微动测试失效；<br/></td><td>压延铜和电解铜弯折寿命、收益及风险评估<br/></td><td>北京开发二部<br/></td><td>12月30日<br/></td><td>2023/3/31<br/></td><td>@李克忠 <br/></td><td>专项验证<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>改成压延铜，M1验证目前效果不大<br/></td></tr>\n<tr>\n<td>45<br/></td><td>点击微动位置选取相对随意且测试规范不统一<br/></td><td>点击微动位置选取相对随意（过严格/过宽松），测试时对于是否同时点击无统一规范。<br/></td><td>北京开发一部<br/></td><td>12月30日<br/></td><td>2023/1/13<br/></td><td>@李克忠 <br/></td><td>20230105：拉通完成<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>点击微动位置升级，本月中旬有初稿<br/></td></tr>\n<tr>\n<td>46<br/></td><td>M18 MIC FPC先组装到电池盖上，在上传BOM时需要确定骨架位置。<br/></td><td>常规项目FPC BOM上传到结构通用料骨架下，但会有一些特殊情况，如M18 MIC FPC需要上传到电池盖预组骨架；上传BOM前需要与结构/整机工艺确认哪条FPC应该上传到那个骨架？<br/></td><td>北京开发一部<br/></td><td>1月6日<br/></td><td><nil><br/></td><td>@宣立杰 <br/></td><td>跟组装顺序有关，每个项目都不一样，上传BOM前确认位置<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>正在实现自动化<br/></td></tr>\n<tr>\n<td>47<br/></td><td>M3 闪光灯FPC维信点胶是全包裹，福莱赢是半包裹，点胶厚度不一致。福莱赢电容与Deco短路<br/></td><td>FPC上阻容感点胶策略，点胶规范<br/></td><td>北京开发二部<br/></td><td>2月2日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>chip：全包裹，X,Y向常规0.6，极限0.3mm；Z向最大0.3mm<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>辅料  点胶 预折弯后续建立统一规范文件。材料库增加点胶胶水型号。<br/></td></tr>\n<tr>\n<td>48<br/></td><td>M18 OLED FPC样品，出现明显色差，供应商需求增加酸洗制程，是否会对铜面造成微蚀，从而影响弯折性能。<br/></td><td>问题描述：维信oled FPC减短验证样品铜面异色，拉力和电测均pass，已安排加做热应力测试发生原因：铜箔原材料领取直接叠板压胶，电压烘烤后bonding面有一层抗氧化膜经过高温烘烤后固化，前处理不容易清洗掉外层蚀刻后板面色差;改善措施：多层板bonding铜箔使用酸洗流程去除抗氧化膜<br/></td><td>北京开发一部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>制程分析酸洗会有0.3~0.5um的咬蚀量，建议N代预研阶段开始弯折验证ok后再导入；M18现阶段不建议增加酸洗制程；鹏鼎下料后无酸洗制程，未反馈铜面异色的问题。<br/></td><td>是<br/></td><td><nil><br/></td><td><nil><br/></td><td>目前关闭<br/></td></tr>\n<tr>\n<td>49<br/></td><td>FPC 各厂商预折弯效果差异明显，导致点击微动测试结果有差异。<br/></td><td>FPC预折弯各厂商差异明显，需要统一验收标准，减少差异。<br/></td><td>上海开发部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@李艳禄 <br/></td><td>规范供应商试产与量产阶段弯折治具设计与弯折参数；<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>统一整理，与上面重复<br/></td></tr>\n<tr>\n<td>50<br/></td><td>N18转轴预研尽量早点把方案同步给PDU<br/></td><td>N18转轴预研开模后再同步给项目设计FPC，发板时间紧张，方案考虑不充分。需求尽量早一些同步方案给PDU。<br/></td><td>北京开发一部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@张建刚 <br/></td><td>N18目前两个主要技术点：五明治和Cavity，预研方案验证pass，五明治ADCP总结中，POF优化验证中<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>目前整理预研报告<br/></td></tr>\n<tr>\n<td>51<br/></td><td>M16,M20,M9 点击微动测试点在连接器边缘，导致连接器锡裂。<br/></td><td>测试点位选择需要避开连接器，加进测试规范。<br/></td><td>上海开发部<br/></td><td>2月24日<br/></td><td><nil><br/></td><td>@苑冉旭 <br/></td><td>新增规范,点击头中心放在弯折区,偏离弯折区边缘<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td>点击微动<br/></td></tr>\n<tr>\n<td>52<br/></td><td>整理笔电设计规范<br/></td><td>B21首项目布局和走线问题较多<br/></td><td>北京开发二部<br/></td><td>20230303<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td>3月3号后，开始梳理，先搜集文档与规范<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>53<br/></td><td>基带摄像头相关规范影响堆叠<br/></td><td>摄像头本体同面与电感距离规范需要拉通统一策略，当前基带中台规范为2.5mm<br/></td><td>深圳开发部<br/></td><td>2023/02/27<br/></td><td><nil><br/></td><td>@吴明华 <br/></td><td><nil><br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>54<br/></td><td>N代产品量产转昌平工厂，相关DFA要求较为严格<br/></td><td>在N代产品堆叠FIX之前与智能制造拉通昌平工厂相关DFA要求<br/></td><td>深圳开发部<br/></td><td>2023/03/10<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>\n<td>55<br/></td><td>对于FPC上的NTC电阻是否点胶无统一标准<br/></td><td>M1 FLASS FPC未了解决散热问题，将原0402 NTC电阻移动至闪光灯附近，由于空间限制，更换为01005，担心撞件，需求点胶，但热设计不希望点胶，从而获得更准确的温度0630：新增器件距离闪光灯距离要求确认？<br/></td><td>北京开发一部<br/></td><td>2023/3/31<br/></td><td><nil><br/></td><td>@李克忠 <br/></td><td>基于M18项目验证结论，点胶对于温度灵敏度有影响，NTC不点胶<br/></td><td>否<br/></td><td><nil><br/></td><td><nil><br/></td><td><nil><br/></td></tr>\n<tr>', 'docName': 'PCB开发部需求跟踪', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4JnCuaSfHTCQYsask5iMdWb', 'sheetName': '', 'owner': '', 'update_time': '2025-03-27 13:53:31', 'doc_type': 'sheet'}, {'title': 'MIPCB Design Checklist总表', 'content': '| 序号 | 模块 | 检查内容 | 图示说明 | 检查阶段 | 风险等级 | 备注说明 | 规范更新时间 | 自检 | 复检 | Fail项说明 | Fail项评审结论 | 备注 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n| 3.24999999999998 | 芯片点胶目前点胶边4mm内器件都有可能溢胶，非点胶边1.5mm器件可能溢胶，玻璃体及板震电容需移开：板震电容定义：0603及以上4.7uf/10uf/22uf/47uf陶瓷电容，VSYS、VPH_PWR、VBAT、VBUS(充电)、VRECT(充电)、VMID(充电)、CFLY(充电)、VBST(Smart PA)移不开的需平行于点胶路径摆放并与基带/工艺达成一致 |  | Placement | M |  |  |  |  |  |  |  |  |', 'docName': 'MIPCB Design Checklist总表', 'docUrl': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'sheetName': '3-生产工艺(Cavity)', 'owner': '', 'update_time': '2025-03-26 20:37:24', 'doc_type': 'sheet'}, {'title': 'O81A DDR点胶工艺可行性评估', 'content': 'O81A DDR 点胶工艺可行性评估\n**结构：低风险；DFR：低风险；仿真：中风险；**  \n**工艺风险共识：**  \n1. **DDR 点胶未有项目量产及试产批量验证经验，只在 O2 摸底过点胶技术方案且未进行可靠性评估，建议优先考虑两件式屏蔽罩方案**\n2. **DDR 原则上不允许加胶，维修拆胶会造成 CPU 物料连带报废，维修风险高**  \n**点胶路径：**  \n单边点胶，DDR 两边均可作为点胶边  \n![](boxk4CIojWxA3Emi7pQpohpJQdg)  \n**屏蔽罩开窗要求：**  \n开孔边到元件边需 ≥1.2mm，目前上下两边不满足，需扩大开窗  \n![](boxk4dzjvJHw17XKXRv8FDnLdbZ)  \n**点胶敏感器件避让：**  \n需确认哪些器件不允许溢胶粘胶，以下为规范要求  \n![](boxk4eyVW4pwFAWztgFBMJ2O4Ff)  \n![](boxk4KwiGqD0yb1PGagR0wSa81g)', 'docName': 'O81A DDR点胶工艺可行性评估', 'docUrl': 'https://xiaomi.f.mioffice.cn/docx/doxk4R3Fmfew5aUicllh6CQxVAc', 'sheetName': '', 'owner': '', 'update_time': '2025-05-06 10:01:03', 'doc_type': 'doc'}]2025-07-11 22:50:51.577 | INFO | request_id=ecc1eaf3-912a-489b-8c98-4f0abb83d58f | services.rerank_service:rerank:53 | Reranker 后处理成功，共获取到 11 个文档




reranked_retrieved_docs: [{'id': '17521623233545967', 'content': 'PN编码: 1351A4012T0B1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012_主板_T0B_主板_T0B_10层任意阶_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00445742_1', 'doc_name': 'PN编码: 1351A4012T0B1', 'doc_typ
e': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.02027840985142551}, {'id': '17521633440779239', 'content': 'PN编码: 1351A4012T0B1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HW
A4012_主板_T0B_主板_T0B_10层任意阶_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00470324_1', 'doc_name': 'PN编码: 1351A4012T0B1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Head
er 3': ''}, 'score': 0.020022393466376867}, {'id': '17521756205749795', 'content': 'PN编码: 1350102001008\nPN中文描述\n线路板_刚性线路板_刚性线路板组件_HWA4012顶部天线垫高板_Sensor-HighBoard_P0~MP_0.35_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00770696_1', 'doc_name': 'P
N编码: 1350102001008', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.018435091879075282}, {'id': '17521772839745157', 'content': 'PN编码: 1350102001008\nPN中文描述\n线
路板_刚性线路板_刚性线路板组件_HWA4012顶部天线垫高板_Sensor-HighBoard_P0~MP_0.35_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00810375_1', 'doc_name': 'PN编码: 1350102001008', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', '
Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.018038266482249883}, {'id': '17521755707776390', 'content': 'PN编码: 1351A4012P0A1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012主板P0A_主板_P0A_10层任意阶_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 
'PN_00769371_1', 'doc_name': 'PN编码: 1351A4012P0A1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.019074750727689355}, {'id': '17521761707796633', 'content': 'PN编码:
 1351A4012P0A1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012主板P0A_主板_P0A_10层任意阶_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00783899_1', 'doc_name': 'PN编码: 1351A4012P0A1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, '
doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.01885525994630217}, {'id': '17521761254026427', 'content': 'PN编码: 1430301000128\nPN中文描述\n显示屏_显示屏子物料_CG_蓝思-0.5mmCG_本色_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00782733_
1', 'doc_name': 'PN编码: 1430301000128', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.015513013956997359}, {'id': '17521691349302413', 'content': 'PN编码: 1351A4012T0C
1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012预投模主板_主板_T0C_343+_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00611541_1', 'doc_name': 'PN编码: 1351A4012T0C1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'H
eader 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.01953399297797638}, {'id': '17521689397896687', 'content': 'PN编码: 1351A4012T0C1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012预投模主板_主板_T0C_343+_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_0
0606818_1', 'doc_name': 'PN编码: 1351A4012T0C1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.019300892744876147}, {'id': '17521772840049051', 'content': 'PN编码: 1350
102001009\nPN中文描述\n线路板_刚性线路板_刚性线路板组件_HWA4012侧边天线_Sensor-HighBoard_P0~MP_0.4MM_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00810376_1', 'doc_name': 'PN编码: 1350102001009', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1
.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.018233884635614518}, {'id': '17521756206032501', 'content': 'PN编码: 1350102001009\nPN中文描述\n线路板_刚性线路板_刚性线路板组件_HWA4012侧边天线_Sensor-HighBoard_P0~MP_0.4MM_HWA4012', 'user_id': 'zty'
, 'metadata_json': {'doc_id': 'PN_00770697_1', 'doc_name': 'PN编码: 1350102001009', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.018642131216549403}, {'id': '175217612
53441374', 'content': 'PN编码: 1430301000125\nPN中文描述\n显示屏_显示屏子物料_CG_BYD-0.4mmCG_本色_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00782731_1', 'doc_name': 'PN编码: 1430301000125', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0,
 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.015260488704472108}, {'id': '17521761254319220', 'content': 'PN编码: 1430301000130\nPN中文描述\n显示屏_显示屏子物料_CG_LIPO加工费用_本色_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_007827
34_1', 'doc_name': 'PN编码: 1430301000130', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.015643630466924213}, {'id': '17521761253739981', 'content': 'PN编码: 143030100
0126\nPN中文描述\n显示屏_显示屏子物料_CG_BYD-0.4mmCG_本色_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00782732_1', 'doc_name': 'PN编码: 1430301000126', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述'
, 'Header 2': '', 'Header 3': ''}, 'score': 0.01538533264954077}, {'id': '17521623233843969', 'content': 'PN编码: 1352A4012T0B1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012_CAVITY_T0B_CAVITY板_T0B_363+_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00445743_1', 'doc_
name': 'PN编码: 1352A4012T0B1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.020542820216311816}, {'id': '17521605201642713', 'content': 'PN编码: 1351A4012T0A1\nPN中文
描述\n线路板_刚性线路板_刚性线路板光板_主板T0A_主板_T0A_10层任意阶_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00401592_1', 'doc_name': 'PN编码: 1351A4012T0A1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN
中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.017848007730347296}, {'id': '17521633440472425', 'content': 'PN编码: 1352A4012T0B1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_HWA4012_CAVITY_T0B_CAVITY板_T0B_363+_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00470323
_1', 'doc_name': 'PN编码: 1352A4012T0B1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.019774377593360995}, {'id': '17521604870941500', 'content': 'PN编码: 1351A4012T0
A1\nPN中文描述\n线路板_刚性线路板_刚性线路板光板_主板T0A_主板_T0A_10层任意阶_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00400829_1', 'doc_name': 'PN编码: 1351A4012T0A1', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Head
er 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.01766289110687451}, {'id': '17521761254605907', 'content': 'PN编码: 1430301000132\nPN中文描述\n显示屏_显示屏子物料_CG_BYD-0.5mmCG_本色_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00782735_1', 'doc_name': 'PN
编码: 1430301000132', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'PN中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.01577728457010518}, {'id': '17521704866942616', 'content': 'PN编码: 1353A4012T0A3\nPN中文描述\n线路
板_柔性线路板_柔性线路板光板_SUB_FPC_fpc_T0A_孔镀_双层_PI_黑色_通孔_HWA4012', 'user_id': 'zty', 'metadata_json': {'doc_id': 'PN_00644782_1', 'doc_name': 'PN编码: 1353A4012T0A3', 'doc_type': '', 'chunk_idx': 0, 'n_chunk': 1, 'source': 'ipd', 'importance': 1.0, 'doc_url': '', 'Header 1': 'P
N中文描述', 'Header 2': '', 'Header 3': ''}, 'score': 0.016344499544580508}]

2025-07-12 12:04:28.517 | INFO | request_id=6beaaa04-ed5b-4b8b-9ac6-d8016c484564 | services.rerank_service:rerank:53 | Reranker 后处理成功，共获取到 20 个文档




doc: {'id': '17524773177339755', 'content': 'MIPCB Design Checklist总表\n4-可靠性\n【内容】\n| 4.21 | WLCSP建议距离卡扣≥5mm，距离螺钉≥2.50mm，距离RF同轴座和测试点≥2mm。注：小米产品当前很少有卡扣设计，如有需要check。 |  | Placement | H |  |  |  |  |  |  |  |  |\n| 4.22 | 同网络连接规范，玻璃体除了充电芯片外，其他所有玻璃体封装禁止同网络连接(玻璃体有开裂风险） |  | Placement | H |  | 2024/12/30 |  |  |  |  |  |  |\n| 4.23 | 陶瓷电容/电感 | 陶瓷电容和电感属于易失效的应力敏感器件1、不建议背跨屏蔽罩焊盘，建议平行于屏蔽罩布局或屏蔽框焊盘取消避让；2、不建议背跨背面大型IC（单边尺寸＞8mm）四条边，且禁止长边横跨在芯片四条边延长线1mm以内；3、402以上的不建议长边方向垂直于大芯片四边布局，201和1005根据应力水平调整。4、不建议背跨结构筋位、卡座边沿、补强钢片边沿、BTB边沿，按照仿真平行于应力带方向布局；5、BTB周边201器件长边平行与BTB长边，402及以上需根据仿真结果调整方向；6、不建议电容长边垂直于BTB短边方向；7、CPU和UFS中间禁止放置402以上的电容，402的电容及以上如布局需要长边平行于芯片边。 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.24 | 磁珠 | 402及以上磁珠勿横跨垫高板边沿 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.25 | 大电容 大电感 | 1、603以上封装大电容 大电感不推荐背面半跨BTB 屏蔽罩焊盘 钢补强边缘 Camera本体边缘 三明治边缘，需要尽量保证焊盘两端受力均衡2 、SUB小板禁止2016电感半跨BTB（电感本体不允许一部分在BTB投影里面 一部分在外面）（此条红线）3 、推荐SUB小板2016电感优先布局在BTB本体与BTB焊盘外面4、主芯片与UFS DDR之间应力集中区，建议不放402及以上器件5、402及以上器件器件长边方向与主芯片 UFS方向平行 | 可靠性失效分析数据库V1.10 | Placement | H |  | 2024/9/2 |  |  |  |  |  |\n| 4.26 | PCB悬臂梁 | 器件尽量避开PCB悬臂梁应力变化最大区域与 PCB窄脖子区域，电容长边推荐于单板变形方向垂直.长边平行于应力带 | [map[id:14 type:embed-image]] | Placement | H |  |  |  |  |  |  |  |\n| 4.27 | 温冲 | 1 增加《温冲失效设计指导V1》，从2022年9月1号所有未KO新项目需执行温冲仿真，目前失效判据：锡裂标准0.13 功能失效0.19 仿真出来超过0.19阈值失效PIN建议外围加大钢网（务必与单板工艺达成一致，只能是非玻璃体BGA最外圈有机会外扩）3 3mm*3mm或单边＞3mm以上WLCSP芯片不背贴补强钢片。 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.28 | 点胶 | 点胶芯片，如SOC和POP-DDR等填充面积＞80%芯片面积；胶水原则上不允许溢胶到芯片表面，如果溢胶到表面 不允许Z向粘连屏蔽框等；点胶不允许溢胶到禁止点胶器件，如WLCSP，绕线电感，薄膜电阻，部分板震敏感电容等；点胶边4mm非点胶边1.50mm点胶不允许溢胶到屏蔽框和结构件等造成点胶器件与结构件等粘连带来应力风险； |  | Placement | H |  |  |  |  |  |  |  |\n| 4.29 | 分板位 | 同轴座子、RF SWICTH、 BTB、SIM卡 易赃污器件 距离分板位不小于1mm（目前单板工艺规范）， 玻璃体、MIC、A+G、气压计器件距离分板位需要2mm |  | Placement | H |  |  |  |  |  |  |  |\n| 4.30 | RF Cable测试座测试点弹片 | 同轴座子、RFSWITCH、装备测试点、弹片器件背面不要放晶振 MIC 玻璃体等应力敏感器件，做好单板支撑，禁止放置2mmX2mm以及以上尺寸BGA器件，建议2mm距离 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.31 | 卡扣位 | 陶瓷电容/电感、玻璃体、MIC、晶体等应力敏感器件距离卡扣建议不小于2mm |  | Placement | H |  |  |  |  |  |  |  |\n| 4.32 | 接插件 | 耳机座/USB座等接插件周圈1mm以内应力敏感器件需满足仿真阈值； |  | Placement | H |  |  |  |  |  |  |  |', 'user_id': 'zty', 'metadata_json': {'doc_id': '20337', 'chunk_idx': 25, 'n_chunk': 95, 'doc_type': 'sheet', 'publish_time': '2025-03-26 20:37:24', 'project_area': '011', 'secrecy_level': 'secret', 'tm': '2025-07-13 12:16:33', 'owner': '{"account":"于树雷","avatar240":"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/4965cfa0-88dd-4835-9123-8c1cac24f10l~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp","avatar72":"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/4965cfa0-88dd-4835-9123-8c1cac24f10l~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp","createTime":*************,"dept":"手机部-硬件工程部-工程开发部-北京开发一部-PCB公共开发部","email":"<EMAIL>","hrStatus":"A","id":1093539,"openId":"ou_36e1cdae8b820471d174128fea4351fd","openIdNew":"ou_2bcab2657c3e1616a978e7cf09983e5f","password":"","updateTime":*************,"username":"yushulei"}', 'create_time': '2025-03-26 18:16:23', 'create_user': 'yushulei', 'update_time': '2025-03-26 20:37:24', 'update_user': 'yushulei', 'source': 'ipd', 'title': 'MIPCB Design Checklist总表', 'url': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'importance': 100.0}, 'score': 0.023809523809523808}

doc: 

{'id': '17524773177339755', 'content': 'MIPCB Design Checklist总表\n4-可靠性\n【内容】\n| 4.21 | WLCSP建议距离卡扣≥5mm，距离螺钉≥2.50mm，距离RF同轴座和测试点≥2mm。注：小米产品当前很少有卡扣设计，如有需要check。 |  | Placement | H |  |  |  |  |  |  |  |  |\n| 4.22 | 同网络连接规范，玻璃体除了充电芯片外，其他所有玻璃体封装禁止同网络连接(玻璃体有开裂风险） |  | Placement | H |  | 2024/12/30 |  |  |  |  |  |  |\n| 4.23 | 陶瓷电容/电感 | 陶瓷电容和电感属于易失效的应力敏感器件1、不建议背跨屏蔽罩焊盘，建议平行于屏蔽罩布局或屏蔽框焊盘取消避让；2、不建议背跨背面大型IC（单边尺寸＞8mm）四条边，且禁止长边横跨在芯片四条边延长线1mm以内；3、402以上的不建议长边方向垂直于大芯片四边布局，201和1005根据应力水平调整。4、不建议背跨结构筋位、卡座边沿、补强钢片边沿、BTB边沿，按照仿真平行于应力带方向布局；5、BTB周边201器件长边平行与BTB长边，402及以上需根据仿真结果调整方向；6、不建议电容长边垂直于BTB短边方向；7、CPU和UFS中间禁止放置402以上的电容，402的电容及以上如布局需要长边平行于芯片边。 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.24 | 磁珠 | 402及以上磁珠勿横跨垫高板边沿 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.25 | 大电容 大电感 | 1、603以上封装大电容 大电感不推荐背面半跨BTB 屏蔽罩焊盘 钢补强边缘 Camera本体边缘 三明治边缘，需要尽量保证焊盘两端受力均衡2 、SUB小板禁止2016电感半跨BTB（电感本体不允许一部分在BTB投影里面 一部分在外面）（此条红线）3 、推荐SUB小板2016电感优先布局在BTB本体与BTB焊盘外面4、主芯片与UFS DDR之间应力集中区，建议不放402及以上器件5、402及以上器件器件长边方向与主芯片 UFS方向平行 | 可靠性失效分析数据库V1.10 | Placement | H |  | 2024/9/2 |  |  |  |  |  |\n| 4.26 | PCB悬臂梁 | 器件尽量避开PCB悬臂梁应力变化最大区域与 PCB窄脖子区域，电容长边推荐于单板变形方向垂直.长边平行于应力带 | [map[id:14 type:embed-image]] | Placement | H |  |  |  |  |  |  |  |\n| 4.27 | 温冲 | 1 增加《温冲失效设计指导V1》，从2022年9月1号所有未KO新项目需执行温冲仿真，目前失效判据：锡裂标准0.13 功能失效0.19 仿真出来超过0.19阈值失效PIN建议外围加大钢网（务必与单板工艺达成一致，只能是非玻璃体BGA最外圈有机会外扩）3 3mm*3mm或单边＞3mm以上WLCSP芯片不背贴补强钢片。 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.28 | 点胶 | 点胶芯片，如SOC和POP-DDR等填充面积＞80%芯片面积；胶水原则上不允许溢胶到芯片表面，如果溢胶到表面 不允许Z向粘连屏蔽框等；点胶不允许溢胶到禁止点胶器件，如WLCSP，绕线电感，薄膜电阻，部分板震敏感电容等；点胶边4mm非点胶边1.50mm点胶不允许溢胶到屏蔽框和结构件等造成点胶器件与结构件等粘连带来应力风险； |  | Placement | H |  |  |  |  |  |  |  |\n| 4.29 | 分板位 | 同轴座子、RF SWICTH、 BTB、SIM卡 易赃污器件 距离分板位不小于1mm（目前单板工艺规范）， 玻璃体、MIC、A+G、气压计器件距离分板位需要2mm |  | Placement | H |  |  |  |  |  |  |  |\n| 4.30 | RF Cable测试座测试点弹片 | 同轴座子、RFSWITCH、装备测试点、弹片器件背面不要放晶振 MIC 玻璃体等应力敏感器件，做好单板支撑，禁止放置2mmX2mm以及以上尺寸BGA器件，建议2mm距离 |  | Placement | H |  |  |  |  |  |  |  |\n| 4.31 | 卡扣位 | 陶瓷电容/电感、玻璃体、MIC、晶体等应力敏感器件距离卡扣建议不小于2mm |  | Placement | H |  |  |  |  |  |  |  |\n| 4.32 | 接插件 | 耳机座/USB座等接插件周圈1mm以内应力敏感器件需满足仿真阈值； |  | Placement | H |  |  |  |  |  |  |  |', 'user_id': 'zty', 'metadata_json': 

{'doc_id': '20337', 'chunk_idx': 25, 'n_chunk': 95, 'doc_type': 'sheet', 'publish_time': '2025-03-26 20:37:24', 'project_area': '011', 'secrecy_level': 'secret', 'tm': '2025-07-13 12:16:33', 'owner': '{"account":"于树雷","avatar240":"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/4965cfa0-88dd-4835-9123-8c1cac24f10l~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp","avatar72":"https://internal-api-lark-file.f.mioffice.cn/static-resource/v1/4965cfa0-88dd-4835-9123-8c1cac24f10l~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp","createTime":*************,"dept":"手机部-硬件工程部-工程开发部-北京开发一部-PCB公共开发部","email":"<EMAIL>","hrStatus":"A","id":1093539,"openId":"ou_36e1cdae8b820471d174128fea4351fd","openIdNew":"ou_2bcab2657c3e1616a978e7cf09983e5f","password":"","updateTime":*************,"username":"yushulei"}', 'create_time': '2025-03-26 18:16:23', 'create_user': 'yushulei', 'update_time': '2025-03-26 20:37:24', 'update_user': 'yushulei', 'source': 'ipd', 'title': 'MIPCB Design Checklist总表', 'url': 'https://xiaomi.f.mioffice.cn/sheets/shtk4qvvSKHzDzxHcqYV2Hoqvob', 'importance': 100.0}, 'score': 0.023809523809523808}






2025-07-19 14:04:37.855 | ERROR | request_id=YOU_UUID | api.routes:generate_stream:358 | DATAQA流式生成错误: cannot access local variable 'response' where it is not associated with a value，耗时: 60.01秒


curl -X 'POST' \
  'http://s-20250717115833-ihp9w.ak-cloudml.xiaomi.srv/v1/chat/completions' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer sk-dms-Qwen3-32B-2025-07-17' \
  -H 'Content-Type: application/json' \
  -d '{
  "model": "Qwen3-32B",
  "stream": true,
  "temperature": 0.5,
  "top_p": 0.5,
  "max_tokens": 2048,
  "messages": [
    {
      "content": "string",
      "role": "developer",
      "name": "string"
    },
    {
      "content": "你是一个专业的问答助手",
      "role": "system",
      "name": "string"
    },
    {
      "content": "你好",
      "role": "user",
      "name": "string"
    }
  ],
  "return_tokens_as_token_ids": true,
  "additionalProp1": {}
}'


curl -X POST "http://10.29.227.159:8000/v1/chat/completions"   -H "Authorization: Bearer sk-dms-Qwen3-32B-2025-05-23"   -H "Content-Type: application/json"   -d '{
    "model": "Qwen3-32B",
    "messages": [{"role": "user", "content": "Hello, world!"}],
    "temperature": 0.7,
    "top_p": 0.95,
    "max_tokens": 512,
    "stream": true
  }'


curl -X POST "http://s-20250717115833-ihp9w.ak-cloudml.xiaomi.srv/v1/chat/completions"   -H "Authorization: Bearer sk-dms-Qwen3-32B-2025-07-17"   -H "Content-Type: application/json"   -d '{
    "model": "Qwen3-32B",
    "messages": [{"role": "user", "content": "Hello, world!"}],
    "temperature": 0.7,
    "top_p": 0.95,
    "max_tokens": 512,
    "stream": true
  }'



  curl -X 'POST' \
  'http://s-20250717114025-1tpip.ak-cloudml.xiaomi.srv/rerank' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer sk-dms-qwen3-reranker-2025-07-17' \
  -H 'Content-Type: application/json' \
  -d '{
  "model": "qwen3-reranker-0.6B-dms-2025-07-17",
  "query": "你好",
  "documents": [
    "hello"
  ],
  "top_n": 0,
  "truncate_prompt_tokens": 1,
  "additional_data": "string",
  "priority": 0,
  "additionalProp1": {}
}'


# Rerank API 配置
RERANK_API_URL=http://***********:8005/rerank
RERANK_API_KEY=sk-dms-qwen3-reranker-8B-2025-06-11



{"id": "*****************", 
"content": "", 
"user_id": "", 

"metadata_json": 
{"doc_id": "399708", "doc_type": "doc", "publish_time": "2024-10-18 19:24:03.0", "project_area": "013\t", "secrecy_level": "secret", "doc_url": "https://xiaomi.f.mioffice.cn/docx/doxk4hJnq7KC4WWdAP9QoX6ePKd", "doc_name": "M1 项目结构复盘报告", "tm": "2025-07-09 23:58:32", 

"owner": "

{"account":"王梓牟","avatar240":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ko_3c2e5da5-9647-4697-8ec0-7312283bdceg~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp","avatar72":"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ko_3c2e5da5-9647-4697-8ec0-7312283bdceg~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp","createTime":*************,"dept":"手机部-硬件工程部-工程开发部-北京开发一部-结构工艺部","email":"<EMAIL>","hrStatus":"A","id":1207498,"openId":"ou_89ed367cb58dd374148f3a7e61802789","password":"","updateTime":*************,"username":"wangzimou"}"

, "create_time": "2023-03-29 15:09:13", "create_user": "wangzimou", "update_time": "2023-07-11 21:03:11", "update_user": "", "source": "ipd", "chunk_idx": 0, "n_chunk": 1}

, "score": 0.*****************}


curl -X POST http://search.misearch.test.b2c.srv/query   -H "Content-Type: application/json"   -d '{
    "user_id": "user_id",
    "db_name": "",
    "token": "",
    "query": "PCB Design Checklist",
    "additional_params": {
      "top_k": 2,
      "search_mode": "hybrid",
      "fusion_method": "rrf",
      "sparse_weight": 0.3,
      "dense_weight": 0.7,
      "rrf_k": 60
    },
    "collection_name": "knowledge_base_hardware"
  }'

